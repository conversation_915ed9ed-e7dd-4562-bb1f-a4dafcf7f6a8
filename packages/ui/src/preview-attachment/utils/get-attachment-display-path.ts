import { FileType } from '../../file/utils/enum';
import { isWhatFileType, renderFileIconUrl } from '../../file/utils/file-type';
import { AttachmentVOExtends } from '../interface';
import { ISimpleAttachment } from '../preview-image';

const getImageUrl = (attach: AttachmentVOExtends | File, isOriginFile = false) => {
  try {
    if ('type' in attach) {
      return URL.createObjectURL(attach);
    }

    if (attach.originFile) {
      // 初次上传，数据还没有保存到数据库
      return URL.createObjectURL(attach.originFile);
    }

    if (isOriginFile && attach.links?.previewUrl) {
      return attach.links?.previewUrl;
    }

    return attach.links?.thumbnailUrl || '';
  } catch (e) {
    console.error('get image url error:', e, 'attach:', attach, 'isOriginFile:', isOriginFile);
    return '';
  }
};

export const getAttachmentDisplayPath = (attach: AttachmentVOExtends | File, isOriginFile = false) => {
  const fileType = isWhatFileType({ name: attach.name, type: 'mimeType' in attach ? attach.mimeType : attach.type });
  switch (fileType) {
    case FileType.Image:
      return getImageUrl(attach, isOriginFile);
    default:
      return renderFileIconUrl({ name: attach.name, type: 'mimeType' in attach ? attach.mimeType : attach.type });
  }
};

export const getAttachmentDisplayUrl = (attach: ISimpleAttachment | File, isOriginFile = false) => {
  const fileType = isWhatFileType({ name: attach.name, type: 'mimeType' in attach ? attach.mimeType : attach.type });
  switch (fileType) {
    case FileType.Image:
      return getImageUrl(attach, isOriginFile);
    default:
      return renderFileIconUrl({ name: attach.name, type: 'mimeType' in attach ? attach.mimeType : attach.type });
  }
};
